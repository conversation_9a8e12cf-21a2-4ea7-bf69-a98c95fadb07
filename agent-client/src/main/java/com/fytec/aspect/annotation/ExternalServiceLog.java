package com.fytec.aspect.annotation;

import com.fytec.constant.ExternalConstants;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.lang.annotation.*;

/**
 * 外部服务调用监控注解
 * 用于标记需要监控的外部服务调用方法，自动记录调用日志和计费信息
 *
 * 使用示例：
 * @ExternalServiceLog(
 *     serviceType = ServiceType.TTS_PREMIUM_SHORT,
 *     strategy = BillingStrategy.BY_CHARACTERS,
 *     logType = LogType.TTS_SHORT,
 *     contentParam = "text"
 * )
 * public R<Object> textToSpeech(@RequestParam String text) { ... }
 *
 * <AUTHOR>
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ExternalServiceLog {

    /**
     * 服务类型
     */
    ExternalConstants.ServiceType serviceType();

    /**
     * 内容参数路径（支持JSONPath表达式，必须指定）
     * 例如：{"$.text", "$.payload.text.text", "$.data.result"}
     * 支持数组索引：{"$.users[0].name", "$.items[*].content"}
     * 支持请求参数路径和响应数据路径
     * 内容类型不传默认作为str处理
     */
    Path2Type[] contents();


    boolean needPreHandle() default false;

    /**
     * 数据处理模式
     */
    DataMode dataMode() default DataMode.REQUEST;

    /**
     * 是否异步记录日志
     */
    boolean async() default true;

    /**
     * 服务描述
     */
    String description() default "";

    /**
     * 数据处理模式枚举
     */
    @Getter
    @AllArgsConstructor
    enum DataMode {
        REQUEST("处理请求数据"),
        RESPONSE("处理响应数据");

        private final String description;
    }

    /**
     * 数据处理模式枚举
     */
    @Getter
    @AllArgsConstructor
    enum FieldProcessType {
        MULTIPART_FILE_AUDIO_DURATION("上传文件音频时长提取"),
        URL_FILE_AUDIO_DURATION("文件链接音频时长提取"),
        STRING_TEXT("Str文本提取"),
        SSE_BASE64_AUDIO_DURATION("SSE语音时长计算"),
        BASE64_AUDIO_DURATION("SSE语音时长计算"),
        FILE_AUDIO_DURATION("SSE语音时长计算")
        ;

        private final String description;
    }

    @interface Path2Type {
        String contentPath();
        FieldProcessType processType() default FieldProcessType.STRING_TEXT;
    }

}