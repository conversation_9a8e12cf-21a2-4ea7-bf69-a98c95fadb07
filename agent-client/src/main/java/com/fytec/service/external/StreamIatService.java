package com.fytec.service.external;

import com.fasterxml.jackson.databind.JsonNode;
import com.fytec.dto.external.IatRequestDTO;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import java.util.function.Function;

/**
 * 流式语音识别服务接口
 * 支持多平台语音识别服务，便于后续扩展和替换
 *
 * <AUTHOR>
 */
public interface StreamIatService {

    /**
     * 识别音频文件（流式处理）
     *
     * @param file 音频文件
     * @param emitter SSE发射器
     * @param timeoutMs 超时时间（毫秒）
     * @return 会话ID
     */
    String recognizeAudioFile(MultipartFile file, SseEmitter emitter, Long timeoutMs);

    /**
     * 识别音频文件（默认超时）
     *
     * @param file 音频文件
     * @param emitter SSE发射器
     * @return 会话ID
     */
    default String recognizeAudioFile(MultipartFile file, SseEmitter emitter) {
        return recognizeAudioFile(file, emitter, 600000L);
    }

    /**
     * 识别音频文件（Controller层专用）
     *
     * @param file 音频文件
     * @param timeout 超时时间（毫秒）
     * @return SSE发射器
     */
    SseEmitter recognizeAudioFile(MultipartFile file, Long timeout);

    /**
     * 识别音频文件（使用完整参数）
     *
     * @param file 音频文件
     * @param request IAT请求参数DTO
     * @param emitter SSE发射器
     * @return 会话ID
     */
    String recognizeAudioFile(MultipartFile file, IatRequestDTO request, SseEmitter emitter);

    /**
     * 识别音频文件（Controller层专用，使用完整参数）
     *
     * @param file 音频文件
     * @param request IAT请求参数DTO
     * @return SSE发射器
     */
    SseEmitter recognizeAudioFile(MultipartFile file, IatRequestDTO request);

    /**
     * 同步语音识别
     *
     * @param file    音频文件
     * @param timeout 超时时间（毫秒）
     * @return 识别结果列表
     */
    Object recognizeAudioFileSync(MultipartFile file, Long timeout);

    /**
     * 同步语音识别（使用完整参数）
     *
     * @param file    音频文件
     * @param request IAT请求参数DTO
     * @return 识别结果列表
     */
    Object recognizeAudioFileSync(MultipartFile file, IatRequestDTO request);

    /**
     * 同步语音识别（支持自定义响应处理器）
     *
     * @param file      音频文件
     * @param timeout   超时时间（毫秒）
     * @param processor 响应处理器
     * @return 识别结果列表
     */
    default Object recognizeAudioFileSync(MultipartFile file, Long timeout, 
                                         Function<JsonNode, Object> processor) {
        return recognizeAudioFileSync(file, timeout);
    }

    /**
     * 获取服务提供商名称
     *
     * @return 提供商名称
     */
    String getProviderName();

    /**
     * 检查服务是否可用
     *
     * @return 是否可用
     */
    boolean isAvailable();

}